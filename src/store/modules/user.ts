/**
 * 用户状态管理模块
 * 管理用户登录状态、用户信息等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo, LoginParams } from '../../api/types'
import { userApi } from '../../api'
import { storage } from '../../utils'

export const useUserStore = defineStore('user', () => {
  // ===== 状态定义 =====
  
  /** 用户认证令牌 */
  const token = ref<string>('')
  
  /** 用户基本信息 */
  const userInfo = ref<UserInfo | null>(null)
  
  /** 用户设置 */
  const userSettings = ref<Record<string, any>>({})

  // ===== 计算属性 =====
  
  /** 是否已登录 */
  const isLoggedIn = computed(() => {
    return !!token.value && !!userInfo.value
  })
  
  /** 用户昵称（优先显示昵称，其次用户名） */
  const displayName = computed(() => {
    if (!userInfo.value) return ''
    return userInfo.value.nickname || userInfo.value.username || ''
  })
  
  /** 用户头像（有默认头像） */
  const avatar = computed(() => {
    return userInfo.value?.avatar || '/static/images/default-avatar.png'
  })

  // ===== 方法定义 =====
  
  /**
   * 用户登录
   */
  async function login(params: LoginParams): Promise<void> {
    try {
      const result = await userApi.login(params)
      
      // 保存token和用户信息
      token.value = result.token
      userInfo.value = result.userInfo
      
      // 持久化存储token
      storage.set('token', result.token)
      storage.set('userInfo', result.userInfo)
      
      // 如果有刷新token，也保存
      if (result.refreshToken) {
        storage.set('refreshToken', result.refreshToken)
      }
      
      console.log('用户登录成功:', result.userInfo.username)
    } catch (error) {
      console.error('用户登录失败:', error)
      throw error
    }
  }
  
  /**
   * 退出登录
   */
  async function logout(): Promise<void> {
    try {
      // 调用退出登录API
      await userApi.logout()
    } catch (error) {
      console.error('退出登录API调用失败:', error)
      // 即使API调用失败，也要清除本地状态
    } finally {
      // 清除本地状态
      token.value = ''
      userInfo.value = null
      userSettings.value = {}
      
      // 清除持久化存储
      storage.remove('token')
      storage.remove('userInfo')
      storage.remove('refreshToken')
      
      console.log('用户已退出登录')
    }
  }
  
  /**
   * 获取用户信息
   */
  async function fetchUserInfo(): Promise<void> {
    try {
      const info = await userApi.getUserInfo()
      userInfo.value = info
      
      // 更新持久化存储
      storage.set('userInfo', info)
      
      console.log('用户信息更新成功')
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  }
  
  /**
   * 更新用户信息
   */
  async function updateUserInfo(updates: Partial<UserInfo>): Promise<void> {
    try {
      const updatedInfo = await userApi.updateUserInfo(updates)
      userInfo.value = updatedInfo
      
      // 更新持久化存储
      storage.set('userInfo', updatedInfo)
      
      console.log('用户信息更新成功')
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  }
  
  /**
   * 获取用户设置
   */
  async function fetchUserSettings(): Promise<void> {
    try {
      const settings = await userApi.getUserSettings()
      userSettings.value = settings
      
      console.log('用户设置获取成功')
    } catch (error) {
      console.error('获取用户设置失败:', error)
      throw error
    }
  }
  
  /**
   * 更新用户设置
   */
  async function updateUserSettings(settings: Record<string, any>): Promise<void> {
    try {
      await userApi.updateUserSettings(settings)
      userSettings.value = { ...userSettings.value, ...settings }
      
      console.log('用户设置更新成功')
    } catch (error) {
      console.error('更新用户设置失败:', error)
      throw error
    }
  }
  
  /**
   * 刷新token
   */
  async function refreshToken(): Promise<void> {
    try {
      const refreshTokenValue = storage.get('refreshToken')
      if (!refreshTokenValue) {
        throw new Error('没有刷新token')
      }
      
      const result = await userApi.refreshToken(refreshTokenValue)
      
      // 更新token和用户信息
      token.value = result.token
      userInfo.value = result.userInfo
      
      // 更新持久化存储
      storage.set('token', result.token)
      storage.set('userInfo', result.userInfo)
      
      if (result.refreshToken) {
        storage.set('refreshToken', result.refreshToken)
      }
      
      console.log('Token刷新成功')
    } catch (error) {
      console.error('Token刷新失败:', error)
      // Token刷新失败，清除登录状态
      await logout()
      throw error
    }
  }
  
  /**
   * 初始化用户状态（从本地存储恢复）
   */
  function initUserState(): void {
    const savedToken = storage.get('token')
    const savedUserInfo = storage.get('userInfo')
    
    if (savedToken && savedUserInfo) {
      token.value = savedToken
      userInfo.value = savedUserInfo
      console.log('用户状态已从本地存储恢复')
    }
  }
  
  /**
   * 检查登录状态
   */
  async function checkLoginStatus(): Promise<boolean> {
    if (!token.value) {
      return false
    }
    
    try {
      // 尝试获取用户信息来验证token是否有效
      await fetchUserInfo()
      return true
    } catch (error) {
      // token无效，清除登录状态
      await logout()
      return false
    }
  }

  // ===== 返回状态和方法 =====
  return {
    // 状态
    token,
    userInfo,
    userSettings,
    
    // 计算属性
    isLoggedIn,
    displayName,
    avatar,
    
    // 方法
    login,
    logout,
    fetchUserInfo,
    updateUserInfo,
    fetchUserSettings,
    updateUserSettings,
    refreshToken,
    initUserState,
    checkLoginStatus
  }
}, {
  // 持久化配置
  persist: {
    key: 'user-store',
    storage: {
      getItem: (key: string) => storage.get(key),
      setItem: (key: string, value: any) => storage.set(key, value),
      removeItem: (key: string) => storage.remove(key)
    },
    // 只持久化token和userInfo
    paths: ['token', 'userInfo']
  }
})
