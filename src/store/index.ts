/**
 * Pinia状态管理入口文件
 * 配置Pinia实例和持久化插件
 */

import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'
import { storage } from '../utils'

// ===== 创建Pinia实例 =====
export const pinia = createPinia()

// ===== 配置持久化插件 =====
pinia.use(
  createPersistedState({
    // 全局持久化配置
    storage: {
      getItem: (key: string) => {
        return storage.get(key)
      },
      setItem: (key: string, value: any) => {
        storage.set(key, value)
      },
      removeItem: (key: string) => {
        storage.remove(key)
      }
    },
    // 序列化配置
    serializer: {
      serialize: JSON.stringify,
      deserialize: JSON.parse
    }
  })
)

// ===== 导出Store模块 =====
export { useUserStore } from './modules/user'

// ===== 默认导出 =====
export default pinia
