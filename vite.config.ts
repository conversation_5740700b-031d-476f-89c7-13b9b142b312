import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  
  // 路径别名配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@api': resolve(__dirname, 'src/api'),
      '@store': resolve(__dirname, 'src/store'),
      '@types': resolve(__dirname, 'src/types'),
      '@styles': resolve(__dirname, 'src/styles'),
      '@static': resolve(__dirname, 'src/static')
    }
  },

  // CSS预处理器配置
  css: {
    preprocessorOptions: {
      scss: {
        // 自动导入全局SCSS变量和混入
        additionalData: `
          @import "@/styles/variables.scss";
          @import "@/styles/mixins.scss";
        `
      }
    }
  },

  // 环境变量配置
  define: {
    __PLATFORM__: JSON.stringify(process.env.UNI_PLATFORM)
  },

  // 开发服务器配置
  server: {
    port: 3000,
    host: '0.0.0.0', // 支持局域网访问
    open: true,
    cors: true
  },

  // 构建配置
  build: {
    target: 'es2015',
    cssTarget: 'chrome61',
    rollupOptions: {
      output: {
        manualChunks: {
          // 将第三方库单独打包
          vendor: ['vue', 'pinia'],
          vant: ['vant']
        }
      }
    }
  },

  // 优化配置
  optimizeDeps: {
    include: ['vue', 'pinia', 'vant']
  }
})
